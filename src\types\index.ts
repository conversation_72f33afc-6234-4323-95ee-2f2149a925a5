// 基础类型定义

export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  locale: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TestResult {
  id: string;
  userId?: string;
  testType: TestType;
  answers: TestAnswer[];
  result: TestResultData;
  shareToken?: string;
  createdAt: Date;
}

export interface TestAnswer {
  questionId: string;
  answer: string | number | string[];
  timestamp: Date;
}

export interface TestResultData {
  title: string;
  description: string;
  score?: number;
  category: string;
  insights: string[];
  recommendations: string[];
  accuracy: number;
}

export type TestType =
  | 'tarot'
  | 'astrology'
  | 'numerology'
  | 'crystal'
  | 'palmistry'
  | 'dreams';

// 博客文章状态枚举
export enum BlogPostStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  SCHEDULED = 'scheduled',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
  DELETED = 'deleted',
}

// 扩展的博客文章接口（与数据库模型对应）
export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  content: string;
  coverImage?: string;
  locale: Locale;
  category: string; // 分类slug
  tags: string[]; // 标签数组
  status: BlogPostStatus;
  publishedAt?: Date | undefined;
  scheduledAt?: Date | undefined;
  readingTime: number;
  viewCount: number;
  likeCount: number;
  shareCount: number;
  commentCount: number;
  featured: boolean;

  // SEO字段
  seoTitle?: string;
  seoDescription?: string;
  keywords: string[];

  // 扩展元数据（包含AI信息等）
  metadata?: {
    ai?: AIGeneratedMetadata;
    relatedPosts?: string[];
    images?: ProcessedImage[];
    [key: string]: any;
  };

  createdAt: Date;
  updatedAt: Date;

  // 关联数据（查询时填充）
  categoryData?: BlogCategory;
  tagData?: BlogTag[];
  author?: BlogAuthor;
}

// 博客作者接口
export interface BlogAuthor {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  bio?: string;
  socialLinks?: {
    twitter?: string;
    linkedin?: string;
    website?: string;
  };
  postCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// 博客分类接口（与数据库模型对应）
export interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color?: string;
  icon?: string;
  image?: string;
  locale: Locale;
  postCount: number;
  seoTitle?: string;
  seoDescription?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 博客标签接口（与数据库模型对应）
export interface BlogTag {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color?: string;
  locale: Locale;
  postCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// AI生成元数据接口
export interface AIGeneratedMetadata {
  model: string;
  generatedAt: Date;
  prompt?: string;
  confidence?: number;
  processingTime?: number;
  qualityScore?: number;
}

export interface TarotCard {
  id: string;
  name: string;
  slug: string;
  arcana: 'major' | 'minor';
  suit?: 'cups' | 'wands' | 'swords' | 'pentacles';
  number?: number;
  image: string;
  meanings: {
    upright: string[];
    reversed: string[];
  };
  description: string;
  keywords: string[];
}

export interface ZodiacSign {
  id: string;
  name: string;
  slug: string;
  symbol: string;
  element: 'fire' | 'earth' | 'air' | 'water';
  quality: 'cardinal' | 'fixed' | 'mutable';
  rulingPlanet: string;
  dateRange: {
    start: string; // MM-DD format
    end: string; // MM-DD format
  };
  traits: {
    positive: string[];
    negative: string[];
  };
  compatibility: string[];
  description: string;
}

export interface NumerologyNumber {
  number: number;
  name: string;
  meaning: string;
  traits: string[];
  challenges: string[];
  opportunities: string[];
}

export interface Crystal {
  id: string;
  name: string;
  slug: string;
  color: string;
  chakra: string[];
  properties: string[];
  uses: string[];
  image: string;
  description: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 表单类型
export interface ContactForm {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export interface NewsletterForm {
  email: string;
  locale: string;
}

// SEO类型
export interface SEOData {
  title: string;
  description: string;
  keywords: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product';
  locale?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
}

// 导航类型
export interface NavigationItem {
  label: string;
  href: string;
  icon?: string;
  children?: NavigationItem[];
  external?: boolean;
}

// 测试问题类型
export interface TestQuestion {
  id: string;
  type: 'single' | 'multiple' | 'scale' | 'text';
  question: string;
  options?: TestOption[];
  required: boolean;
  order: number;
}

export interface TestOption {
  id: string;
  text: string;
  value: string | number;
  image?: string;
}

// 统计类型
export interface SiteStats {
  totalUsers: number;
  totalTests: number;
  totalPosts: number;
  averageAccuracy: number;
  popularTests: Array<{
    type: TestType;
    count: number;
  }>;
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// 主题类型
export type Theme = 'light' | 'dark' | 'system';

// 语言类型
export type Locale = 'en' | 'zh-CN' | 'zh-TW' | 'es' | 'pt' | 'hi' | 'ja';

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop';

// 排序类型
export type SortOrder = 'asc' | 'desc';

export interface SortOption {
  field: string;
  order: SortOrder;
  label: string;
}

// 过滤类型
export interface FilterOption {
  key: string;
  value: string | number | boolean;
  label: string;
}

// 分页类型
export interface PaginationOptions {
  page: number;
  limit: number;
  sort?: SortOption;
  filters?: FilterOption[];
}

// 搜索类型
export interface SearchOptions extends PaginationOptions {
  query: string;
  category?: string;
  tags?: string[];
}

// 缓存类型
export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  tags?: string[];
  revalidate?: boolean;
}

// 分析类型
export interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  timestamp: Date;
  userId?: string;
  sessionId: string;
}

// 博客导入相关类型
export interface ImportCandidate {
  filePath: string;
  fileName: string;
  fileSize: number;
  lastModified: Date;
  frontmatter?: Record<string, any>;
  contentPreview: string;
  images?: string[];
  estimatedReadingTime?: number;
}

export interface ImportResult {
  success: boolean;
  post?: BlogPost;
  originalFile: string;
  error?: string;
  warnings?: string[];
  processedImages?: ProcessedImage[];
}

export interface ProcessedImage {
  originalUrl: string;
  processedUrl: string;
  alt: string;
  caption?: string;
  width: number;
  height: number;
  fileSize: number;
  format: string;
}

// 博客质量检查相关类型
export interface QualityReport {
  overall: QualityScore;
  seo: SEOCheck;
  readability: ReadabilityCheck;
  images: ImageCheck;
  links: LinkCheck;
  duplicates: DuplicateCheck;
  suggestions: string[];
  score: number; // 0-100
}

export interface QualityScore {
  score: number;
  level: 'poor' | 'fair' | 'good' | 'excellent';
  issues: string[];
}

export interface SEOCheck {
  titleLength: { valid: boolean; current: number; optimal: string };
  descriptionLength: { valid: boolean; current: number; optimal: string };
  keywordDensity: { valid: boolean; density: number; keywords: string[] };
  headingStructure: { valid: boolean; issues: string[] };
  metaTags: { valid: boolean; missing: string[] };
  score: number;
}

export interface ReadabilityCheck {
  wordCount: number;
  sentenceLength: { average: number; issues: string[] };
  paragraphLength: { average: number; issues: string[] };
  readingTime: number;
  fleschScore: number;
  readingLevel: string;
}

export interface ImageCheck {
  altTags: { valid: boolean; missing: number; total: number };
  fileSize: { valid: boolean; oversized: string[] };
  format: { valid: boolean; unsupported: string[] };
  loading: { valid: boolean; unoptimized: number };
}

export interface LinkCheck {
  internal: { total: number; broken: string[] };
  external: { total: number; broken: string[] };
  anchors: { total: number; missing: string[] };
}

export interface DuplicateCheck {
  titleSimilarity: { similar: boolean; matches: string[] };
  contentSimilarity: {
    similar: boolean;
    percentage: number;
    matches: string[];
  };
  keywordOverlap: { overlap: boolean; percentage: number; conflicts: string[] };
}

// 博客批量操作类型
export interface BatchPublishOptions {
  autoShare?: boolean;
  interval?: number; // milliseconds between publishes
  notifySubscribers?: boolean;
}

export interface BatchPublishResult {
  results: Array<{
    postId: string;
    success: boolean;
    error?: string;
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
    duration: number;
  };
}

// 评论相关类型
export interface Comment {
  id: string;
  content: string;
  userId?: string;
  userName?: string;
  userAvatar?: string;
  postId: string;
  parentId?: string;
  isApproved: boolean;
  createdAt: Date;
  updatedAt: Date;
  replies?: Comment[];
  likeCount?: number;
  isLiked?: boolean;
  guestName?: string;
  guestEmail?: string;
}

export interface CommentLike {
  id: string;
  commentId: string;
  userId?: string;
  ipAddress?: string;
  createdAt: Date;
}

// 博客统计类型
export interface BlogStats {
  totalPosts: number;
  publishedPosts: number;
  draftPosts: number;
  totalViews: number;
  totalLikes: number;
  totalShares: number;
  averageReadingTime: number;
  topCategories: Array<{
    category: string;
    postCount: number;
    viewCount: number;
  }>;
  topTags: Array<{
    tag: string;
    postCount: number;
    popularity: number;
  }>;
  recentActivity: Array<{
    type: 'publish' | 'update' | 'view' | 'like' | 'share';
    postId: string;
    timestamp: Date;
    metadata?: Record<string, any>;
  }>;
}
