import { PostStatus, TestType, type User } from '@prisma/client';

import { prisma } from './prisma';

// 博客文章相关操作
// Blog post operations

export async function getBlogPosts({
  locale,
  category,
  status = PostStatus.PUBLISHED,
  page = 1,
  limit = 10,
  search,
}: {
  locale?: string;
  category?: string;
  status?: PostStatus;
  page?: number;
  limit?: number;
  search?: string;
}) {
  const skip = (page - 1) * limit;

  const where = {
    ...(locale && { locale }),
    ...(category && { category }),
    status,
    ...(search && {
      OR: [
        { title: { contains: search, mode: 'insensitive' as const } },
        { content: { contains: search, mode: 'insensitive' as const } },
        { tags: { has: search } },
      ],
    }),
  };

  const [posts, total] = await Promise.all([
    prisma.blogPost.findMany({
      where,
      include: {
        views: true,
        comments: {
          where: { isApproved: true },
          include: { user: true },
        },
        favorites: true,
      },
      orderBy: { publishedAt: 'desc' },
      skip,
      take: limit,
    }),
    prisma.blogPost.count({ where }),
  ]);

  return {
    posts,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: page < Math.ceil(total / limit),
      hasPrev: page > 1,
    },
  };
}

export async function getBlogPostBySlug(slug: string, locale?: string) {
  return prisma.blogPost.findFirst({
    where: {
      slug,
      ...(locale && { locale }),
      status: PostStatus.PUBLISHED,
    },
    include: {
      views: true,
      comments: {
        where: { isApproved: true, parentId: null },
        include: {
          user: true,
          replies: {
            where: { isApproved: true },
            include: { user: true },
          },
        },
        orderBy: { createdAt: 'desc' },
      },
      favorites: true,
    },
  });
}

export async function createBlogPost(data: {
  title: string;
  content: string;
  excerpt?: string;
  locale: string;
  category: string;
  tags: string[];
  coverImage?: string;
  seoTitle?: string;
  seoDescription?: string;
  keywords: string[];
  status?: PostStatus;
  metadata?: any;
}) {
  // 生成 slug
  const slug = data.title
    .toLowerCase()
    .replace(/[^a-z0-9\u4e00-\u9fa5]+/g, '-')
    .replace(/^-+|-+$/g, '');

  // 计算阅读时间（基于内容长度）
  const readingTime = Math.ceil(
    data.content.replace(/<[^>]*>/g, '').length / 200
  );

  return prisma.blogPost.create({
    data: {
      ...data,
      slug,
      readingTime,
      status: data.status || PostStatus.DRAFT,
    },
  });
}

export async function updateBlogPost(id: string, data: any) {
  return prisma.blogPost.update({
    where: { id },
    data,
  });
}

export async function deleteBlogPost(id: string) {
  return prisma.blogPost.update({
    where: { id },
    data: { status: PostStatus.DELETED },
  });
}

export async function recordBlogView(
  postId: string,
  userId?: string,
  ipAddress?: string,
  userAgent?: string
) {
  return prisma.blogView.create({
    data: {
      postId,
      userId: userId || null,
      ipAddress: ipAddress || null,
      userAgent: userAgent || null,
    },
  });
}

// 测试结果相关操作
// Test result operations

export async function createTestResult(data: {
  userId?: string;
  testType: TestType;
  answers: any;
  result: any;
  shareToken?: string;
  isPublic?: boolean;
}) {
  return prisma.testResult.create({
    data,
  });
}

export async function getTestResult(id: string) {
  return prisma.testResult.findUnique({
    where: { id },
    include: { user: true },
  });
}

export async function getTestResultByShareToken(shareToken: string) {
  return prisma.testResult.findUnique({
    where: { shareToken },
    include: { user: true },
  });
}

export async function getUserTestResults(userId: string, page = 1, limit = 10) {
  const skip = (page - 1) * limit;

  const [results, total] = await Promise.all([
    prisma.testResult.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit,
    }),
    prisma.testResult.count({ where: { userId } }),
  ]);

  return {
    results,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: page < Math.ceil(total / limit),
      hasPrev: page > 1,
    },
  };
}

// 用户相关操作
// User operations

export async function createUser(data: {
  email: string;
  username?: string;
  avatar?: string;
  locale?: string;
  theme?: string;
}) {
  return prisma.user.create({
    data,
  });
}

export async function getUserByEmail(email: string) {
  return prisma.user.findUnique({
    where: { email },
  });
}

export async function getUserById(id: string) {
  return prisma.user.findUnique({
    where: { id },
    include: {
      testResults: {
        orderBy: { createdAt: 'desc' },
        take: 5,
      },
      favorites: {
        include: { post: true },
        orderBy: { createdAt: 'desc' },
        take: 5,
      },
    },
  });
}

export async function updateUser(id: string, data: Partial<User>) {
  return prisma.user.update({
    where: { id },
    data,
  });
}

// 收藏相关操作
// Favorite operations

export async function addToFavorites(userId: string, postId: string) {
  return prisma.userFavorite.create({
    data: { userId, postId },
  });
}

export async function removeFromFavorites(userId: string, postId: string) {
  return prisma.userFavorite.delete({
    where: {
      userId_postId: { userId, postId },
    },
  });
}

export async function getUserFavorites(userId: string, page = 1, limit = 10) {
  const skip = (page - 1) * limit;

  const [favorites, total] = await Promise.all([
    prisma.userFavorite.findMany({
      where: { userId },
      include: { post: true },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit,
    }),
    prisma.userFavorite.count({ where: { userId } }),
  ]);

  return {
    favorites,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: page < Math.ceil(total / limit),
      hasPrev: page > 1,
    },
  };
}

// 评论相关操作
// Comment operations

export async function createComment(data: {
  content: string;
  userId?: string;
  postId: string;
  parentId?: string;
}) {
  return prisma.comment.create({
    data,
    include: {
      user: true,
      replies: {
        include: { user: true },
      },
    },
  });
}

export async function getComments(postId: string, page = 1, limit = 10) {
  const skip = (page - 1) * limit;

  const [comments, total] = await Promise.all([
    prisma.comment.findMany({
      where: {
        postId,
        parentId: null,
        isApproved: true,
      },
      include: {
        user: true,
        replies: {
          where: { isApproved: true },
          include: { user: true },
          orderBy: { createdAt: 'asc' },
        },
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit,
    }),
    prisma.comment.count({
      where: {
        postId,
        parentId: null,
        isApproved: true,
      },
    }),
  ]);

  return {
    comments,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: page < Math.ceil(total / limit),
      hasPrev: page > 1,
    },
  };
}
