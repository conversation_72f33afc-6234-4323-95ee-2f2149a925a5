import { PrismaClient } from '@prisma/client';

// 全局 Prisma 客户端实例
// Global Prisma client instance

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma =
  globalForPrisma.prisma ??
  new PrismaClient({
    log:
      process.env.NODE_ENV === 'development'
        ? ['query', 'error', 'warn']
        : ['error'],
    errorFormat: 'pretty',
  });

// 在开发环境中防止热重载时创建多个 Prisma 实例
// Prevent multiple Prisma instances during hot reload in development
if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// 数据库连接测试函数
// Database connection test function
export async function testDatabaseConnection() {
  try {
    await prisma.$connect();
    console.log('✅ 数据库连接成功 / Database connected successfully');
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败 / Database connection failed:', error);
    return false;
  }
}

// 优雅关闭数据库连接
// Graceful database disconnection
export async function disconnectDatabase() {
  try {
    await prisma.$disconnect();
    console.log('✅ 数据库连接已关闭 / Database disconnected');
  } catch (error) {
    console.error(
      '❌ 关闭数据库连接时出错 / Error disconnecting database:',
      error
    );
  }
}

// 数据库健康检查
// Database health check
export async function checkDatabaseHealth() {
  try {
    const result = await prisma.$queryRaw`SELECT 1 as health`;
    return { healthy: true, result };
  } catch (error) {
    return {
      healthy: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// 导出 Prisma 类型
// Export Prisma types
export type {
  User,
  BlogPost,
  TestResult,
  Comment,
  UserFavorite,
  BlogView,
  UserSession,
  UserVerification,
  PostStatus,
  TestType,
  VerificationType,
} from '@prisma/client';

// 导出常用的 Prisma 类型组合
// Export common Prisma type combinations
export type BlogPostWithRelations = Prisma.BlogPostGetPayload<{
  include: {
    views: true;
    comments: {
      include: {
        user: true;
        replies: {
          include: {
            user: true;
          };
        };
      };
    };
    favorites: {
      include: {
        user: true;
      };
    };
  };
}>;

export type UserWithRelations = Prisma.UserGetPayload<{
  include: {
    testResults: true;
    blogViews: true;
    comments: true;
    favorites: {
      include: {
        post: true;
      };
    };
    sessions: true;
  };
}>;

export type TestResultWithUser = Prisma.TestResultGetPayload<{
  include: {
    user: true;
  };
}>;

// 导入 Prisma 命名空间用于类型定义
import { Prisma } from '@prisma/client';
export { Prisma };
